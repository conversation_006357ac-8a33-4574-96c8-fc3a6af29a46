# Server Configuration
PORT=3003
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002/archive

# Token Cost Configuration
ANALYSIS_TOKEN_COST=1

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Event-Driven Architecture Configuration
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_ASSESSMENTS=analysis_events_assessments
CONSUMER_PREFETCH=10

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres
DB_SCHEMA=assessment
DB_POOL_MAX=25
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=20000
DB_POOL_EVICT=5000

# Idempotency Configuration
IDEMPOTENCY_ENABLED=true
IDEMPOTENCY_TTL_HOURS=24
IDEMPOTENCY_MAX_CACHE_SIZE=10000
IDEMPOTENCY_CLEANUP_INTERVAL_MINUTES=60

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/assessment-service.log

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Environment
NODE_ENV=development

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
DEAD_LETTER_QUEUE=assessment_analysis_dlq

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Google Generative AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key_here_get_from_google_ai_studio
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7

# Mock AI Configuration (for testing without using paid Gemini API)
# Set to 'true' to use mock AI responses instead of real Gemini API
USE_MOCK_MODEL=true

# Token Counting Configuration
# Enable/disable token counting and usage tracking
# Set to 'false' to disable token counting entirely (emergency fallback)
ENABLE_TOKEN_COUNTING=true

# Usage data retention settings (in days)
# Controls how long usage statistics are kept in memory
# Range: 1-365 days, affects memory usage
# Recommended: 7 (dev), 30 (staging), 90 (production)
TOKEN_USAGE_RETENTION_DAYS=30

# Gemini 2.5 Flash Pricing Configuration (per 1M tokens in USD)
# Updated based on official Google AI pricing as of 2024

# Pricing Tier: 'free' or 'paid'
# Free tier has no cost, paid tier uses the rates below
GEMINI_PRICING_TIER=free

# Paid Tier Pricing (per 1M tokens)
# Text/Image/Video content
GEMINI_INPUT_TOKEN_PRICE=0.30
GEMINI_OUTPUT_TOKEN_PRICE=2.50
GEMINI_CONTEXT_CACHING_PRICE=0.075
GEMINI_CONTEXT_STORAGE_PRICE=1.00

# Audio content (different pricing)
GEMINI_INPUT_AUDIO_PRICE=1.00
GEMINI_CONTEXT_CACHING_AUDIO_PRICE=0.25

# Character to token estimation ratio (approximate)
CHAR_TO_TOKEN_RATIO=4

# Token counting performance settings
# Timeout for token counting API calls (milliseconds)
# Lower values fail faster but may miss counts on slow networks
# Range: 1000-30000ms, Recommended: 3000 (dev), 5000 (prod)
TOKEN_COUNT_TIMEOUT=5000

# Enable fallback token estimation when API fails
# Uses character-based estimation (~4 chars per token)
# Recommended: always 'true' for production reliability
ENABLE_TOKEN_COUNT_FALLBACK=true

# Internal Service Communication Key (same across all services)
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002/archive

# Notification Service Configuration (Optional - if notification service is implemented)
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Assessment Service Configuration
ASSESSMENT_SERVICE_URL=http://localhost:3003

# Worker Configuration
WORKER_CONCURRENCY=10
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=1800000
HEARTBEAT_INTERVAL=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/analysis-worker.log

# Database Optimization Configuration (UPDATED)
DB_BATCH_SIZE=50                    # Naik dari 10 → 50
DB_BATCH_INTERVAL=1500             # Turun dari 5000 → 1500ms
DB_BATCH_MAX_QUEUE=2000            # Baru: max queue size

# HTTP Connection Optimization (BARU)
HTTP_KEEP_ALIVE_TIMEOUT=30000      # 30 detik
HTTP_MAX_SOCKETS_PER_HOST=20       # Per host limit
HTTP_TOTAL_MAX_SOCKETS=100         # Total limit

# Legacy HTTP Configuration (for backward compatibility)
HTTP_KEEP_ALIVE=true
HTTP_MAX_SOCKETS=50
HTTP_MAX_FREE_SOCKETS=10

# Rate Limiting Configuration
RATE_LIMIT_USER_HOUR=5
RATE_LIMIT_IP_HOUR=20
RATE_LIMIT_GLOBAL_MINUTE=100

# Job Deduplication Configuration
JOB_CACHE_RETENTION_MS=3600000
MAX_JOB_CACHE_SIZE=10000

# Audit Logging Configuration
AUDIT_LOG_DIR=logs/audit
AUDIT_ENCRYPTION_KEY=your_audit_encryption_key_here
AUDIT_RETENTION_DAYS=2555

# Token Refund Configuration
ENABLE_TOKEN_REFUND=true
REFUND_PROCESSING_INTERVAL=5000

# Optimized Processor Configuration
USE_OPTIMIZED_PROCESSOR=true

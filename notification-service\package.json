{"name": "atma-notification-service", "version": "1.0.0", "description": "Real-time notification service for ATMA Backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"amqplib": "^0.10.8", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^6.1.5", "joi": "^17.9.2", "jsonwebtoken": "^9.0.0", "socket.io": "^4.7.2", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^7.1.3"}}